package com.polarion.synchronizer.proxy.feishu;

import com.lark.project.service.project.builder.ListProjectWorkItemTypeResp;
import com.polarion.core.util.logging.Logger;
import com.polarion.synchronizer.SynchronizationException;
import com.polarion.synchronizer.model.FieldDefinition;
import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.model.Option;
import com.polarion.synchronizer.model.TransferItem;
import com.polarion.synchronizer.model.UpdateResult;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Date;



/**
 * 飞书项目代理实现类
 * 负责与飞书项目API的具体交互
 */
public class FeishuProxy implements IProxy {
    
    private static final Logger logger = Logger.getLogger(FeishuProxy.class);
    
    private final FeishuProxyConfiguration configuration;
    private final FeishuSDKHelper sdkHelper;
    
    public FeishuProxy(@NotNull FeishuProxyConfiguration configuration) {
        this.configuration = configuration;

        String configurationError = configuration.checkConfiguration();
        if (configurationError != null) {
            throw new SynchronizationException("飞书项目配置错误: " + configurationError + "。请检查飞书项目连接属性。");
        }

        // 初始化SDK Helper
        FeishuConnection connection = (FeishuConnection) configuration.getConnection();
        if (connection == null) {
            throw new SynchronizationException("飞书项目连接配置不能为空");
        }

        this.sdkHelper = new FeishuSDKHelper(connection);

        logger.info("飞书项目代理初始化完成，项目Key: " + configuration.getProjectKey() +
                   ", 认证模式: " + connection.getAuthMode());
    }

	@Override
	public void close() {
		// 关闭资源，清理连接
		if (sdkHelper != null) {
			logger.debug("关闭飞书项目代理连接");
		}
	}

	@Override
	public List<UpdateResult> delete(List<String> itemIds) {
		// TODO: 实现删除工作项的逻辑
		logger.warn("delete方法尚未实现，项目数量: " + (itemIds != null ? itemIds.size() : 0));
		return new ArrayList<>();
	}

	@Override
	public String getContentScope() {
		// 返回内容范围，通常为null
		return null;
	}

	@Override
	public Collection<FieldDefinition> getDefinedFields(String typeId) {
		try {
			logger.debug("获取工作项类型字段定义: " + typeId);

			// 使用飞书项目API获取字段定义
			com.lark.project.service.field.builder.QueryProjectFieldsReq req =
				com.lark.project.service.field.builder.QueryProjectFieldsReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey(typeId)
					.build();

			com.lark.project.service.field.builder.QueryProjectFieldsResp resp =
				sdkHelper.getClient().getFieldService().queryProjectFields(req, sdkHelper.getDefaultOptions());

			if (!resp.success()) {
				logger.warn("获取字段定义失败: " + resp.getErrMsg());
				return getDefaultFieldDefinitions();
			}

			List<FieldDefinition> fieldDefinitions = new ArrayList<>();
			List<com.lark.project.service.field.model.SimpleField> fields = resp.getData();

			if (fields != null) {
				for (com.lark.project.service.field.model.SimpleField field : fields) {
					if (field.getIsObsoleted() != null && field.getIsObsoleted()) {
						continue; // 跳过已废弃的字段
					}

					String fieldKey = field.getFieldKey();
					String fieldName = field.getFieldName() != null ? field.getFieldName() : fieldKey;
					String fieldType = convertFeishuFieldTypeToPolarion(field.getFieldTypeKey());
					boolean isReadOnly = false; // 飞书项目字段一般都可编辑
					boolean isMultiValued = isMultiValuedField(field.getFieldTypeKey());

					FieldDefinition fieldDef = new FieldDefinition(fieldKey, fieldName, fieldType, isReadOnly, isMultiValued);
					fieldDefinitions.add(fieldDef);
				}
			}

			// 添加基本字段
			addBasicFieldDefinitions(fieldDefinitions);

			logger.info("获取到 " + fieldDefinitions.size() + " 个字段定义，工作项类型: " + typeId);
			return fieldDefinitions;

		} catch (Exception e) {
			logger.error("获取字段定义时发生错误，工作项类型: " + typeId, e);
			return getDefaultFieldDefinitions();
		}
	}

	@Override
	public Collection<Option> getDefinedTypes() {
		try {
			logger.debug("获取工作项类型列表");

			// 使用飞书项目API获取工作项类型
			com.lark.project.service.project.builder.ListProjectWorkItemTypeResp resp =
				sdkHelper.getWorkItemTypes(configuration.getProjectKey());

			if (!resp.success()) {
				logger.warn("获取工作项类型失败: " + resp.getErrMsg());
				return getDefaultWorkItemTypes();
			}

			List<Option> options = new ArrayList<>();
			List<com.lark.project.service.workitem.model.WorkItemKeyType> types = resp.getData();

			if (types != null) {
				for (com.lark.project.service.workitem.model.WorkItemKeyType type : types) {
					if (type.getIsDisable() != null && type.getIsDisable() == 1) {
						continue; // 跳过已禁用的类型
					}

					String typeKey = type.getTypeKey();
					String typeName = type.getName() != null ? type.getName() : typeKey;

					Option option = new Option(typeKey, typeName);
					options.add(option);
				}
			}

			logger.info("获取到 " + options.size() + " 个工作项类型");
			return options;

		} catch (Exception e) {
			logger.error("获取工作项类型时发生错误", e);
			return getDefaultWorkItemTypes();
		}
	}

	@Override
	public Collection<Option> getDefinedTypes(Collection<String> requiredTypes) {
		Collection<Option> allTypes = getDefinedTypes();

		if (requiredTypes == null || requiredTypes.isEmpty()) {
			return allTypes;
		}

		// 过滤出需要的类型
		List<Option> filteredTypes = new ArrayList<>();
		Set<String> requiredTypeSet = new HashSet<>(requiredTypes);

		for (Option option : allTypes) {
			if (requiredTypeSet.contains(option.getId())) {
				filteredTypes.add(option);
			}
		}

		logger.debug("过滤后的工作项类型数量: " + filteredTypes.size());
		return filteredTypes;
	}

	@Override
	public Collection<TransferItem> getItems(Collection<String> itemIds, Collection<String> fieldKeys) {
		try {
			logger.debug("获取指定工作项，数量: " + (itemIds != null ? itemIds.size() : 0));

			if (itemIds == null || itemIds.isEmpty()) {
				return new ArrayList<>();
			}

			List<TransferItem> items = new ArrayList<>();

			// 将字符串ID转换为Long类型
			List<Long> workItemIds = new ArrayList<>();
			for (String itemId : itemIds) {
				try {
					workItemIds.add(Long.parseLong(itemId));
				} catch (NumberFormatException e) {
					logger.warn("无效的工作项ID: " + itemId);
				}
			}

			if (workItemIds.isEmpty()) {
				return items;
			}

			// 使用QueryWorkItemDetail API获取工作项详情
			com.lark.project.service.workitem.builder.QueryWorkItemDetailReq req =
				com.lark.project.service.workitem.builder.QueryWorkItemDetailReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey("") // 空字符串表示所有类型
					.workItemIDs(workItemIds)
					.fields(fieldKeys != null ? new ArrayList<>(fieldKeys) : null)
					.build();

			com.lark.project.service.workitem.builder.QueryWorkItemDetailResp resp =
				sdkHelper.getClient().getWorkItemService().queryWorkItemDetail(req, sdkHelper.getDefaultOptions());

			if (!resp.success()) {
				logger.warn("获取工作项详情失败: " + resp.getErrMsg());
				return items;
			}

			// 转换工作项数据
			List<com.lark.project.service.workitem.model.WorkItemInfo> workItems = resp.getData();
			if (workItems != null) {
				for (com.lark.project.service.workitem.model.WorkItemInfo workItem : workItems) {
					TransferItem item = convertWorkItemToTransferItem(workItem, fieldKeys);
					if (item != null) {
						items.add(item);
					}
				}
			}

			logger.info("成功获取 " + items.size() + " 个工作项");
			return items;

		} catch (Exception e) {
			logger.error("获取指定工作项时发生错误", e);
			return new ArrayList<>();
		}
	}

	@Override
	public Collection<TransferItem> getScopeItems(Collection<String> fieldKeys) {
		try {
			logger.debug("获取范围内所有工作项");

			List<TransferItem> allItems = new ArrayList<>();

			// 使用Filter API获取工作项列表
			com.lark.project.service.workitem.builder.FilterReq req =
				com.lark.project.service.workitem.builder.FilterReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.pageNum(1L)
					.pageSize(100L) // 每页100个工作项
					.build();

			// 如果配置了工作项类型过滤，添加类型过滤
			if (configuration.getWorkItemTypes() != null && !configuration.getWorkItemTypes().isEmpty()) {
				String[] typeArray = configuration.getWorkItemTypes().split(",");
				List<String> typeList = new ArrayList<>();
				for (String type : typeArray) {
					typeList.add(type.trim());
				}
				req.getFilterReqBody().setWorkItemTypeKeys(typeList);
			}

			// 分页获取所有工作项
			int currentPage = 1;
			boolean hasMore = true;

			while (hasMore) {
				req.getFilterReqBody().setPageNum((long) currentPage);

				com.lark.project.service.workitem.builder.FilterResp resp =
					sdkHelper.getClient().getWorkItemService().filter(req, sdkHelper.getDefaultOptions());

				if (!resp.success()) {
					logger.warn("获取工作项列表失败: " + resp.getErrMsg());
					break;
				}

				List<com.lark.project.service.workitem.model.WorkItemInfo> workItems = resp.getData();
				if (workItems != null && !workItems.isEmpty()) {
					for (com.lark.project.service.workitem.model.WorkItemInfo workItem : workItems) {
						TransferItem item = convertWorkItemToTransferItem(workItem, fieldKeys);
						if (item != null) {
							allItems.add(item);
						}
					}

					// 检查是否还有更多页面
					if (resp.getPagination() != null && resp.getPagination().getHasMore() != null) {
						hasMore = resp.getPagination().getHasMore();
					} else {
						hasMore = workItems.size() >= 100; // 如果返回的数量等于页面大小，可能还有更多
					}

					currentPage++;
				} else {
					hasMore = false;
				}
			}

			logger.info("成功获取 " + allItems.size() + " 个范围内工作项");
			return allItems;

		} catch (Exception e) {
			logger.error("获取范围内工作项时发生错误", e);
			return new ArrayList<>();
		}
	}

	@Override
	public String getTargetName() {
		// 返回目标系统名称
		return "飞书项目 - " + configuration.getProjectKey();
	}

	@Override
	public boolean hasNonSynchronizableFields() {
		// 飞书项目的字段一般都可以同步
		return false;
	}

	@Override
	public boolean isHierarchySupported() {
		// 飞书项目不支持层次结构
		return false;
	}

	@Override
	public List<UpdateResult> update(List<TransferItem> items) {
		List<UpdateResult> results = new ArrayList<>();

		if (items == null || items.isEmpty()) {
			return results;
		}

		logger.debug("开始处理工作项，数量: " + items.size());

		for (TransferItem item : items) {
			try {
				UpdateResult result;

				// 判断是创建还是更新
				if (item.getId() == null || item.getKey().isForeign) {
					// 创建新工作项
					result = createSingleWorkItem(item);
				} else {
					// 更新现有工作项
					result = updateSingleWorkItem(item);
				}

				results.add(result);
			} catch (Exception e) {
				logger.error("处理工作项失败: " + item.getId(), e);
				results.add(new UpdateResult(item.getId(), "处理失败: " + e.getMessage()));
			}
		}

		int successCount = (int) results.stream().mapToLong(r -> r.isSuccessful() ? 1 : 0).sum();
		int failCount = results.size() - successCount;

		logger.info("工作项处理完成，成功: " + successCount + "，失败: " + failCount);

		return results;
	}

	/**
	 * 创建单个工作项
	 */
	private UpdateResult createSingleWorkItem(TransferItem item) {
		try {
			logger.debug("创建新工作项，类型: " + item.getType());

			// 构建创建请求
			com.lark.project.service.workitem.builder.CreateWorkItemReq req =
				com.lark.project.service.workitem.builder.CreateWorkItemReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey(item.getType() != null ? item.getType() : "task") // 默认类型为task
					.name(item.getTitle() != null ? item.getTitle() : "新工作项")
					.build();

			// 设置字段值
			List<com.lark.project.service.field.model.FieldValuePair> fieldValuePairs = new ArrayList<>();

			// 设置描述
			if (item.getDescription() != null) {
				com.lark.project.service.field.model.FieldValuePair descField = new com.lark.project.service.field.model.FieldValuePair();
				descField.setFieldKey("description");
				descField.setFieldValue(item.getDescription());
				fieldValuePairs.add(descField);
			}

			// 设置自定义字段
			if (item instanceof Item) {
				Item itemImpl = (Item) item;
				Map<String, Object> customFields = itemImpl.getCustomFields();
				if (customFields != null) {
					for (Map.Entry<String, Object> entry : customFields.entrySet()) {
						String fieldKey = entry.getKey();
						Object fieldValue = entry.getValue();

						// 跳过只读字段和系统字段
						if (IProxy.KEY_ITEM_URL.equals(fieldKey) ||
							"work_item_id".equals(fieldKey) ||
							"created_at".equals(fieldKey) ||
							"updated_at".equals(fieldKey) ||
							"creator".equals(fieldKey)) {
							continue;
						}

						com.lark.project.service.field.model.FieldValuePair customField = new com.lark.project.service.field.model.FieldValuePair();
						customField.setFieldKey(fieldKey);
						customField.setFieldValue(convertPolarionValueToFeishu(fieldValue));
						fieldValuePairs.add(customField);
					}
				}
			}

			req.getCreateWorkItemReqBody().setFieldValuePairs(fieldValuePairs);

			// 调用API创建工作项
			com.lark.project.service.workitem.builder.CreateWorkItemResp resp =
				sdkHelper.getClient().getWorkItemService().createWorkItem(req, sdkHelper.getDefaultOptions());

			if (resp.success()) {
				Long createdId = resp.getData();
				String createdIdStr = createdId != null ? createdId.toString() : null;

				// 更新TransferItem的ID
				item.setId(createdIdStr);

				logger.debug("工作项创建成功: " + createdIdStr);
				return new CreateResult(createdIdStr);
			} else {
				logger.warn("工作项创建失败，错误: " + resp.getErrMsg());
				return new CreateResult(null, resp.getErrMsg());
			}

		} catch (Exception e) {
			logger.error("创建工作项时发生异常", e);
			return new CreateResult(null, "创建异常: " + e.getMessage());
		}
	}

	/**
	 * 更新单个工作项
	 */
	private UpdateResult updateSingleWorkItem(TransferItem item) {
		try {
			// 构建更新请求
			com.lark.project.service.workitem.builder.UpdateWorkItemReq req =
				com.lark.project.service.workitem.builder.UpdateWorkItemReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey(item.getType() != null ? item.getType() : "")
					.workItemID(Long.parseLong(item.getId()))
					.build();

			// 设置更新字段
			List<com.lark.project.service.field.model.FieldValuePair> fieldValuePairs = new ArrayList<>();

			// 更新标题
			if (item.getTitle() != null) {
				com.lark.project.service.field.model.FieldValuePair titleField = new com.lark.project.service.field.model.FieldValuePair();
				titleField.setFieldKey("name");
				titleField.setFieldValue(item.getTitle());
				fieldValuePairs.add(titleField);
			}

			// 更新描述
			if (item.getDescription() != null) {
				com.lark.project.service.field.model.FieldValuePair descField = new com.lark.project.service.field.model.FieldValuePair();
				descField.setFieldKey("description");
				descField.setFieldValue(item.getDescription());
				fieldValuePairs.add(descField);
			}

			// 更新自定义字段
			if (item instanceof Item) {
				Item itemImpl = (Item) item;
				Map<String, Object> customFields = itemImpl.getCustomFields();
				if (customFields != null) {
					for (Map.Entry<String, Object> entry : customFields.entrySet()) {
						if (!IProxy.KEY_ITEM_URL.equals(entry.getKey())) { // 跳过只读字段
							com.lark.project.service.field.model.FieldValuePair customField = new com.lark.project.service.field.model.FieldValuePair();
							customField.setFieldKey(entry.getKey());
							customField.setFieldValue(entry.getValue());
							fieldValuePairs.add(customField);
						}
					}
				}
			}

			req.getUpdateWorkItemReqBody().setFieldValuePairs(fieldValuePairs);

			// 调用API更新工作项
			com.lark.project.service.workitem.builder.UpdateWorkItemResp resp =
				sdkHelper.getClient().getWorkItemService().updateWorkItem(req, sdkHelper.getDefaultOptions());

			if (resp.success()) {
				logger.debug("工作项更新成功: " + item.getId());
				return new UpdateResult(item.getId());
			} else {
				logger.warn("工作项更新失败: " + item.getId() + ", 错误: " + resp.getErrMsg());
				return new UpdateResult(item.getId(), resp.getErrMsg());
			}

		} catch (Exception e) {
			logger.error("更新工作项时发生异常: " + item.getId(), e);
			return new UpdateResult(item.getId(), "更新异常: " + e.getMessage());
		}
	}

	/**
	 * 将飞书项目字段类型转换为Polarion字段类型
	 */
	private String convertFeishuFieldTypeToPolarion(String feishuFieldType) {
		if (feishuFieldType == null) {
			return IProxy.TYPE_STRING;
		}

		switch (feishuFieldType.toLowerCase()) {
			case "text":
			case "string":
			case "textarea":
				return IProxy.TYPE_STRING;
			case "rich_text":
			case "richtext":
				return IProxy.TYPE_RICH_TEXT;
			case "number":
			case "integer":
				return IProxy.TYPE_INTEGER;
			case "float":
			case "decimal":
				return IProxy.TYPE_FLOAT;
			case "date":
				return IProxy.TYPE_DATE;
			case "datetime":
			case "timestamp":
				return IProxy.TYPE_DATE_TIME;
			case "boolean":
				return IProxy.TYPE_BOOLEAN;
			case "user":
			case "person":
				return IProxy.TYPE_USER;
			case "option":
			case "enum":
			case "select":
				return IProxy.TYPE_OPTION;
			case "multi_option":
			case "multi_select":
				return IProxy.TYPE_OPTION; // 多选也使用option类型，通过isMultiValued区分
			default:
				logger.debug("未知的飞书字段类型: " + feishuFieldType + "，使用默认类型string");
				return IProxy.TYPE_STRING;
		}
	}

	/**
	 * 判断字段是否为多值字段
	 */
	private boolean isMultiValuedField(String feishuFieldType) {
		if (feishuFieldType == null) {
			return false;
		}

		return feishuFieldType.toLowerCase().contains("multi") ||
			   feishuFieldType.toLowerCase().contains("array") ||
			   feishuFieldType.toLowerCase().contains("list");
	}

	/**
	 * 添加基本字段定义
	 */
	private void addBasicFieldDefinitions(List<FieldDefinition> fieldDefinitions) {
		// 添加基本的必需字段
		fieldDefinitions.add(new FieldDefinition("work_item_id", "工作项ID", IProxy.TYPE_STRING, true, false));
		fieldDefinitions.add(new FieldDefinition("name", "标题", IProxy.TYPE_STRING, false, false));
		fieldDefinitions.add(new FieldDefinition("description", "描述", IProxy.TYPE_RICH_TEXT, false, false));
		fieldDefinitions.add(new FieldDefinition("work_item_type_key", "类型", IProxy.TYPE_STRING, false, false));
		fieldDefinitions.add(new FieldDefinition("status", "状态", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("priority", "优先级", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("assignee", "指派人", IProxy.TYPE_USER, false, false));
		fieldDefinitions.add(new FieldDefinition("reporter", "报告人", IProxy.TYPE_USER, false, false));
		fieldDefinitions.add(new FieldDefinition("creator", "创建人", IProxy.TYPE_USER, true, false));
		fieldDefinitions.add(new FieldDefinition("created_at", "创建时间", IProxy.TYPE_DATE_TIME, true, false));
		fieldDefinitions.add(new FieldDefinition("updated_at", "更新时间", IProxy.TYPE_DATE_TIME, true, false));
		fieldDefinitions.add(new FieldDefinition("due_date", "截止日期", IProxy.TYPE_DATE, false, false));

		// 添加URL字段用于链接
		fieldDefinitions.add(new FieldDefinition(IProxy.KEY_ITEM_URL, "项目链接", IProxy.TYPE_STRING, true, false));
	}

	/**
	 * 获取默认字段定义（当API调用失败时使用）
	 */
	private Collection<FieldDefinition> getDefaultFieldDefinitions() {
		List<FieldDefinition> fieldDefinitions = new ArrayList<>();
		addBasicFieldDefinitions(fieldDefinitions);

		// 添加一些常用的自定义字段
		fieldDefinitions.add(new FieldDefinition("story_points", "故事点", IProxy.TYPE_INTEGER, false, false));
		fieldDefinitions.add(new FieldDefinition("labels", "标签", IProxy.TYPE_OPTION, false, true));
		fieldDefinitions.add(new FieldDefinition("component", "组件", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("version", "版本", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("epic", "史诗", IProxy.TYPE_STRING, false, false));
		fieldDefinitions.add(new FieldDefinition("sprint", "迭代", IProxy.TYPE_STRING, false, false));

		logger.info("使用默认字段定义，共 " + fieldDefinitions.size() + " 个字段");
		return fieldDefinitions;
	}

	/**
	 * 获取默认工作项类型（当API调用失败时使用）
	 */
	private Collection<Option> getDefaultWorkItemTypes() {
		List<Option> options = new ArrayList<>();

		// 添加常见的工作项类型
		options.add(new Option("story", "用户故事"));
		options.add(new Option("task", "任务"));
		options.add(new Option("bug", "缺陷"));
		options.add(new Option("epic", "史诗"));
		options.add(new Option("feature", "特性"));
		options.add(new Option("requirement", "需求"));
		options.add(new Option("test_case", "测试用例"));
		options.add(new Option("issue", "问题"));

		logger.info("使用默认工作项类型，共 " + options.size() + " 个类型");
		return options;
	}

	/**
	 * 将飞书工作项转换为TransferItem
	 */
	private TransferItem convertWorkItemToTransferItem(com.lark.project.service.workitem.model.WorkItemInfo workItem, Collection<String> fieldKeys) {
		try {
			String itemId = workItem.getID() != null ? workItem.getID().toString() : null;
			if (itemId == null) {
				logger.warn("工作项ID为空，跳过转换");
				return null;
			}

			Item item = new Item(itemId);

			// 设置基本属性
			if (workItem.getName() != null) {
				item.setTitle(workItem.getName());
			}

			if (workItem.getWorkItemTypeKey() != null) {
				item.setType(workItem.getWorkItemTypeKey());
			}

			// 设置时间戳
			if (workItem.getCreatedAt() != null) {
				item.setCreatedAt(new Date(workItem.getCreatedAt() * 1000)); // 飞书时间戳是秒，Java需要毫秒
			}

			if (workItem.getUpdatedAt() != null) {
				item.setUpdatedAt(new Date(workItem.getUpdatedAt() * 1000));
			}

			// 设置创建者和更新者
			if (workItem.getCreatedBy() != null) {
				item.setReporter(workItem.getCreatedBy());
			}

			if (workItem.getUpdatedBy() != null) {
				item.setCustomField("updated_by", workItem.getUpdatedBy());
			}

			// 设置状态信息
			if (workItem.getWorkItemStatus() != null) {
				item.setStatus(workItem.getWorkItemStatus().getStatus());
			}

			if (workItem.getSubStage() != null) {
				item.setCustomField("sub_stage", workItem.getSubStage());
			}

			// 处理自定义字段
			if (workItem.getFields() != null) {
				for (com.lark.project.service.field.model.FieldValuePair field : workItem.getFields()) {
					if (field.getFieldKey() != null && field.getFieldValue() != null) {
						// 根据字段类型处理字段值
						Object fieldValue = convertFieldValue(field);
						if (fieldValue != null) {
							item.setCustomField(field.getFieldKey(), fieldValue);
						}
					}
				}
			}

			// 设置项目链接
			String itemUrl = buildWorkItemUrl(workItem);
			if (itemUrl != null) {
				item.setCustomField(IProxy.KEY_ITEM_URL, itemUrl);
			}

			// 过滤字段（如果指定了fieldKeys）
			if (fieldKeys != null && !fieldKeys.isEmpty()) {
				filterTransferItemFields(item, fieldKeys);
			}

			return item;

		} catch (Exception e) {
			logger.error("转换工作项时发生错误: " + workItem.getID(), e);
			return null;
		}
	}

	/**
	 * 转换字段值
	 */
	private Object convertFieldValue(com.lark.project.service.field.model.FieldValuePair field) {
		try {
			Object value = field.getFieldValue();
			if (value == null) {
				return null;
			}

			// 根据字段类型进行转换
			String fieldKey = field.getFieldKey();

			// 处理特殊字段
			if ("description".equals(fieldKey) && value instanceof String) {
				return value; // 描述字段保持原样
			}

			// 处理日期字段
			if (fieldKey.contains("date") || fieldKey.contains("time")) {
				if (value instanceof Number) {
					return new Date(((Number) value).longValue() * 1000);
				}
			}

			// 处理数字字段
			if (value instanceof Number) {
				return value;
			}

			// 处理布尔字段
			if (value instanceof Boolean) {
				return value;
			}

			// 其他情况转换为字符串
			return value.toString();

		} catch (Exception e) {
			logger.warn("转换字段值时发生错误: " + field.getFieldKey(), e);
			return null;
		}
	}

	/**
	 * 构建工作项URL
	 */
	private String buildWorkItemUrl(com.lark.project.service.workitem.model.WorkItemInfo workItem) {
		try {
			FeishuConnection connection = (FeishuConnection) configuration.getConnection();
			String baseUrl = connection.getServerUrl();
			if (baseUrl == null) {
				baseUrl = "https://project.feishu.cn";
			}

			// 构建飞书项目工作项URL
			return baseUrl + "/project/" + workItem.getProjectKey() + "/workitem/" + workItem.getID();

		} catch (Exception e) {
			logger.warn("构建工作项URL时发生错误", e);
			return null;
		}
	}

	/**
	 * 过滤TransferItem字段
	 */
	private void filterTransferItemFields(Item item, Collection<String> fieldKeys) {
		// TODO: 实现字段过滤逻辑
		// 这里可以根据fieldKeys来过滤item中的字段
		logger.debug("字段过滤功能待实现，字段数量: " + fieldKeys.size());
	}
    
  
}
